import request from '@/utils/request.js'

export function getAvailableProducts(query) {
    return request({
        url: '/yanbao/product/getAvailableProducts',
        method: 'get',
        params: query
    })
}

export function getProductInfo(query) {
    return request({
        url: '/yanbao/product/getProductInfo',
        method: 'get',
        params: query
    })
}

export function getProductSuitStore(query) {
    return request({
        url: '/yanbao/product/getProductSuitStore',
        method: 'get',
        params: query
    })
}

// 查询产品管理列表
export function listProduct(query) {
    return request({
        url: '/yanbao/product/list',
        method: 'get',
        params: query
    })
}

// 查询产品管理详细
export function getProduct(id) {
    return request({
        url: '/yanbao/product/' + id,
        method: 'get'
    })
}

// 新增产品管理
export function addProduct(data) {
    return request({
        url: '/yanbao/product',
        method: 'post',
        data: data
    })
}

// 修改产品管理
export function updateProduct(data) {
    return request({
        url: '/yanbao/product',
        method: 'put',
        data: data
    })
}

// 产品状态修改
export function changeProductStatus(id, status) {
    const data = {
        id,
        status
    }
    return request({
        url: '/yanbao/product/changeStatus',
        method: 'put',
        data: data
    })
}

// 获取生效的产品类型列表
export function getActiveProductTypes() {
    return request({
        url: '/yanbao/product/activeProductTypes',
        method: 'get'
    })
}

// 查询产品管理详细
export function getPrice(query) {
    return request({
        url: '/yanbao/product/getPrice',
        method: 'get',
        params: query
    })
}

// 修改产品价格
export function updatePrice(data) {
    return request({
        url: '/yanbao/product/updatePrice',
        method: 'put',
        data: data
    })
}
