package com.ruoyi.yanbao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.yanbao.entity.ProductPrice;
import com.ruoyi.yanbao.entity.vo.ProductPriceVo;

import java.util.List;

/**
 * <p>
 * 产品价格设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProductPriceService extends IService<ProductPrice> {

    List<ProductPriceVo> getPrice(Long productId);

    /**
     * 更新产品价格
     * @param productPrice 产品价格信息
     * @param operator 操作人
     * @return 更新结果
     */
    boolean updatePrice(ProductPrice productPrice, String operator);
}
