package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.yanbao.entity.ProductPrice;
import com.ruoyi.yanbao.entity.vo.ProductPriceVo;
import com.ruoyi.yanbao.mapper.ProductPriceMapper;
import com.ruoyi.yanbao.service.ProductPriceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 产品价格设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProductPriceServiceImpl extends ServiceImpl<ProductPriceMapper, ProductPrice> implements ProductPriceService {

    @Override
    public List<ProductPriceVo> getPrice(Long productId) {
        return baseMapper.getPrice(productId);
    }
}
