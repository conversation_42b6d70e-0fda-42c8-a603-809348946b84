package com.ruoyi.yanbao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.vo.ProductVo;

import java.util.List;

/**
 * <p>
 * 产品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProductService extends IService<Product> {

    /**
     * 查询当前客户可用的保险产品
     *
     * @param storeIds 经销商ID列表
     * @return 产品列表
     */
    List<Product> getAvailableProducts(String storeIds);

    /**
     * 查询产品详情
     *
     * @param productId
     * @return
     */
    ProductVo getProductInfo(Long productId, boolean needProductType, boolean needProductPrice);

    /**
     * 查询产品管理列表
     *
     * @param product 产品信息
     * @return 产品列表
     */
    List<Product> selectProductList(Product product);

    /**
     * 查询产品管理列表（包含关联信息）
     *
     * @param product 产品信息
     * @return 产品列表
     */
    List<ProductVo> selectProductListWithDetails(Product product);

    /**
     * 修改产品状态
     *
     * @param id     产品ID
     * @param status 状态
     * @return 结果
     */
    boolean changeStatus(Long id, Integer status);

    /**
     * 保存产品及其保障期限
     *
     * @param product 产品信息
     * @return 结果
     */
    boolean saveProductWithTerms(ProductVo product);

    /**
     * 更新产品及其保障期限
     *
     * @param product 产品信息
     * @return 结果
     */
    boolean updateProductWithTerms(ProductVo product);
}
