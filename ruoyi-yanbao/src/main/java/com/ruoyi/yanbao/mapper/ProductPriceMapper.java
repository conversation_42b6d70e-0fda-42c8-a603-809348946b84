package com.ruoyi.yanbao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.yanbao.entity.ProductPrice;
import com.ruoyi.yanbao.entity.vo.ProductPriceVo;

import java.util.List;

/**
 * <p>
 * 产品价格设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProductPriceMapper extends BaseMapper<ProductPrice> {

    List<ProductPriceVo> getPrice(Long productId);
    
}
