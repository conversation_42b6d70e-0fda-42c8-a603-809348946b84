<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yanbao.mapper.ProductPriceMapper">

    <resultMap id="voResultMap" type="com.ruoyi.yanbao.entity.vo.ProductPriceVo">
        <result column="id" property="id"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="changed_at" property="changedAt"/>
        <result column="changed_by" property="changedBy"/>
        <result column="is_delete" property="isDelete"/>
        <result column="product_id" property="productId"/>
        <result column="product_term_id" property="productTermId"/>
        <result column="car_brand_id" property="carBrandId"/>
        <result column="car_series_id" property="carSeriesId"/>
        <result column="max_car_price" property="maxCarPrice"/>
        <result column="min_car_price" property="minCarPrice"/>
        <result column="vehicle_age_mileage_id" property="vehicleAgeMileageId"/>
        <result column="suggestion_price" property="suggestionPrice"/>
        <result column="insurance_price" property="insurancePrice"/>
        <result column="service_price" property="servicePrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="store_id" property="storeId"/>
        <result column="product_term_name" property="productTermName"/>
        <result column="vehicle_age_mileage_name" property="vehicleAgeMileageName"/>
        <result column="store_name" property="storeName"/>
        <result column="car_brand_name" property="carBrandName"/>
        <result column="car_series_name" property="carSeriesName"/>
    </resultMap>


    <select id="getPrice" parameterType="java.lang.Long" resultMap="voResultMap">
        SELECT b.`name` as product_term_name
        ,c.`name` as vehicle_age_mileage_name
        ,d.`name` as store_name
        ,e.`name` as car_brand_name
        ,f.`name` as car_series_name
        ,a.*
        from p_product_price a
        LEFT JOIN p_product_term b on a.product_term_id=b.id and b.is_delete=0
        LEFT JOIN p_vehicle_age_mileage c on a.vehicle_age_mileage_id=c.id and c.is_delete=0
        LEFT JOIN p_store d on a.store_id=d.id and d.is_delete=0
        LEFT JOIN p_car_brand e on a.car_brand_id=e.id and e.is_delete=0
        LEFT JOIN p_car_series f on a.car_series_id=f.id and f.is_delete=0
        where a.is_delete=0 and a.product_id=#{productId} order by d.id asc,b.time_limit asc,b.mileage
        asc,c.begin_mileage
        asc,c.begin_vehicle_age asc,e.id asc,f.id asc,a.min_car_price asc
    </select>

</mapper>
