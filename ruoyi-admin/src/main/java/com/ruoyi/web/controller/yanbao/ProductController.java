package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductPrice;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.entity.vo.ProductPriceVo;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.service.InsuranceCompanyService;
import com.ruoyi.yanbao.service.ProductPriceService;
import com.ruoyi.yanbao.service.ProductService;
import com.ruoyi.yanbao.service.ProductTypeService;
import com.ruoyi.yanbao.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/yanbao/product")
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductTypeService productTypeService;

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ProductPriceService productPriceService;

    /**
     * 查下当前客户可用的保险产品
     */
    @GetMapping("/getAvailableProducts")
    public AjaxResult getAvailableProducts() {
        return AjaxResult.success(productService.getAvailableProducts(getLoginUser().getUser().getStoreIds()));
    }

    @GetMapping("/getProductInfo")
    public AjaxResult getProductInfo(Long productId) {
        ProductVo product = productService.getProductInfo(productId, true, true);
        if (product == null) {
            return AjaxResult.error("产品不存在");
        }
        return AjaxResult.success(product);
    }

    /**
     * 获取产品适用门店
     *
     * @param productId
     * @return
     */
    @GetMapping("/getProductSuitStore")
    public AjaxResult getProductSuitStore(Long productId) {
        String storeIds = getLoginUser().getUser().getStoreIds();
        List<Store> stores = new ArrayList<>();
        if (StringUtils.isNotEmpty(storeIds)) {
            stores = storeService.listByIds(Arrays.asList(storeIds.split(",")));
        } else {
            stores = storeService.selectStoreAll(Constants.STATUS_ENABLE);
        }
        stores = stores.stream().filter(s -> s.getStatus() == Constants.STATUS_ENABLE).collect(Collectors.toList());
        if (productId != null) {
            Product product = productService.getById(productId);
            if (product != null && StringUtils.isNotEmpty(product.getSuitStoreIds())) {
                List<String> storeIdList = Arrays.asList(product.getSuitStoreIds().split(","));
                stores = stores.stream().filter(s -> storeIdList.contains(s.getId().toString())).collect(Collectors.toList());
            }
        }
        return AjaxResult.success(stores);
    }

    /**
     * 查询产品管理列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:list')")
    @GetMapping("/list")
    public TableDataInfo list(Product product) {
        startPage();
        List<ProductVo> list = productService.selectProductListWithDetails(product);
        return getDataTable(list);
    }

    /**
     * 获取产品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        ProductVo productVo = productService.getProductInfo(id, false, true);
        return success(productVo);
    }

    /**
     * 新增产品管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:add')")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductVo product) {
        product.setCreatedBy(getUsername());
        return toAjax(productService.saveProductWithTerms(product));
    }

    /**
     * 修改产品管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductVo product) {
        product.setChangedBy(getUsername());
        return toAjax(productService.updateProductWithTerms(product));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody Product product) {
        return toAjax(productService.changeStatus(product.getId(), product.getStatus()));
    }

    /**
     * 获取生效的产品类型列表
     */
    @GetMapping("/activeProductTypes")
    public AjaxResult getActiveProductTypes() {
        List<ProductType> productTypes = productTypeService.selectActiveProductTypes();
        return success(productTypes);
    }

    @GetMapping("/getPrice")
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:list')")
    public AjaxResult getPrice(Long productId) {
        AjaxResult result = new AjaxResult();
        if (productId != null) {
            Product product = productService.getById(productId);
            if (product != null) {
                if (StringUtils.isNotBlank(product.getVehicleAgeMileageIds())) {
                    result.put("vehicleAgeMileageIds", product.getVehicleAgeMileageIds().split(","));
                }
                result.put("priceType", product.getPriceType());
                result.put("priceRule", product.getPriceRule());
            }
            List<ProductPriceVo> price = productPriceService.getPrice(productId);
            result.put("priceItems", price);
            return result;
        }
        result.put("priceItems", new ArrayList<>());
        return result;
    }

    /**
     * 修改产品价格
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:editPrice')")
    @Log(title = "产品价格管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePrice")
    public AjaxResult updatePrice(@RequestBody ProductPrice productPrice) {
        return toAjax(productPriceService.updatePrice(productPrice, getUsername()));
    }
}
